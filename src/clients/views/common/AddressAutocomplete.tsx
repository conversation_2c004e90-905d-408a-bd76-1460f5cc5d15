'use client';

import { useCallback, useMemo, useState } from 'react';

import Autocomplete, { AutocompleteOption } from '@/clients/ui/autocomplete';
import { getListingAddresses } from '@/app/actions/property';
import { ListingAddressPayload } from '@/types/property';
import { twMerge } from 'tailwind-merge';
import { Popover, PopoverTrigger } from '@/clients/ui/popover';
import Input from '@/clients/ui/input';

type Props = {
  className?: string;
  wrapperClassName?: string;
  placeholder?: string;
  value?: string;
  onSelectAddress?: (_a: ListingAddressPayload) => void;
  name?: string;
  disabled?: boolean;
  onClear?: () => void;
};

const AddressAutocomplete = ({
  className = '',
  wrapperClassName = '',
  placeholder = '',
  value = '',
  onSelectAddress,
  name,
  disabled,
  onClear,
}: Props) => {
  const [address, setAdress] = useState<string>(value);
  const [isFetching, setIsFetching] = useState<boolean>(false);
  const [addresses, setAddresses] = useState<ListingAddressPayload[]>([]);

  const options = useMemo(
    () =>
      addresses?.map((_address) => ({
        id: _address.listing_id,
        label: _address.address,
        value: _address.address,
      })),
    [addresses]
  );

  const onSelect = useCallback(
    (option: AutocompleteOption) => {
      setAdress(option.value);
      const address = addresses?.find((_a) => _a.listing_id === option.id);
      if (address && onSelectAddress) {
        onSelectAddress?.(address);
      }
    },
    [addresses, onSelectAddress]
  );

  const fetchAddresses = useCallback((query = '') => {
    setIsFetching(true);
    getListingAddresses<{ results: ListingAddressPayload[] }>(query)
      .then(({ results }) => {
        setAddresses(results);
        setIsFetching(false);
      })
      .catch((err) => {
        console.error(err);
        setIsFetching(false);
      });
  }, []);

  const fetchDataDebounced = useMemo(
    () =>
      debounce((val) => {
        fetchData?.(val);
      }, 500),
    [fetchData]
  );

  const onChangeQuery = useCallback(
    (event: any) => {
      const { value } = event.target;
      setQuery(value);
      onChangeValue?.(value);
      fetchDataDebounced(value);
      if (!showPopup) {
        setShowPopup(true);
      }
    },
    [onChangeValue, fetchDataDebounced, showPopup]
  );

  const onClickedTrigger = useCallback((e: any) => {
    e.preventDefault();
  }, []);

  const onOpenChange = useCallback(
    (open: boolean) => {
      setShowPopup(open);
      if (!open) {
        if (query !== value) {
          if (query.length === 0) {
            onChangeValue?.('');
          } else if (value && value.length > 0 && options.length > 0) {
            setQuery(value);
          } else if (query.length > 0) {
            onChangeValue?.(query);
          }
        }
        onFocussed?.(false);
      }
    },
    [query, value, onFocussed, options.length, onChangeValue]
  );

  return (
    <div className={twMerge('relative w-full', wrapperClassName)}>
      <Popover open={showPopup} onOpenChange={onOpenChange}>
        <PopoverTrigger asChild onClick={onClickedTrigger}>
          <div className="relative">
            <Input
              name={name}
              className={twMerge(`px-2.5 py-[14px] w-full text-sm`, className)}
              value={query}
              onChange={onChangeQuery}
              placeholder={placeholder}
              onFocus={() => {
                if (query.length === 0) {
                  setShowPopup(true);
                  setActiveIndex(0);
                  onFocussed?.(true);
                }
              }}
              disabled={disabled}
            />
            {onClear && query.length > 0 && (
              <XMarkIcon
                onClick={onClear}
                className="absolute cursor-pointer right-2 top-1/2 -translate-y-1/2 w-4 h-4"
              />
            )}
          </div>
        </PopoverTrigger>

        <PopoverContent
          className={twMerge(
            'w-[var(--radix-popover-trigger-width)] p-0 max-h-[200px] overflow-y-auto',
            dropdownClassName
          )}
          align="start"
          sideOffset={4}
        >
          <AutocompleteItems
            options={options}
            activeIndex={activeIndex}
            setActiveIndex={setActiveIndex}
            onSelect={onSelect}
            onClosePopup={() => setShowPopup(false)}
            isFetchingData={isFetchingData}
            itemClassName={itemClassName}
            dropdownClassName=""
          />
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default AddressAutocomplete;
